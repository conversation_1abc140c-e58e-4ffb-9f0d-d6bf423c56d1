import{g as p,a as g,s as b,_ as e,r as m,u as v,b as x,j as T,d as f,e as R}from"./index-BG5vYnqD.js";function C(s){return p("MuiToolbar",s)}g("MuiToolbar",["root","gutters","regular","dense"]);const G=["className","component","disableGutters","variant"],j=s=>{const{classes:t,disableGutters:a,variant:o}=s;return R({root:["root",!a&&"gutters",o]},C,t)},y=b("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(s,t)=>{const{ownerState:a}=s;return[t.root,!a.disableGutters&&t.gutters,t[a.variant]]}})(({theme:s,ownerState:t})=>e({position:"relative",display:"flex",alignItems:"center"},!t.disableGutters&&{paddingLeft:s.spacing(2),paddingRight:s.spacing(2),[s.breakpoints.up("sm")]:{paddingLeft:s.spacing(3),paddingRight:s.spacing(3)}},t.variant==="dense"&&{minHeight:48}),({theme:s,ownerState:t})=>t.variant==="regular"&&s.mixins.toolbar),U=m.forwardRef(function(t,a){const o=v({props:t,name:"MuiToolbar"}),{className:r,component:i="div",disableGutters:l=!1,variant:c="regular"}=o,u=x(o,G),n=e({},o,{component:i,disableGutters:l,variant:c}),d=j(n);return T.jsx(y,e({as:i,className:f(d.root,r),ref:a,ownerState:n},u))});export{U as T};
