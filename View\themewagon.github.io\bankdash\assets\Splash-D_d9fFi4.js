import{q as i,j as o}from"./index-BG5vYnqD.js";import{P as e}from"./Portal-IcPWo0MN.js";import{B as t}from"./Box-Be8rAmCf.js";const s=i`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(1turn);
  }
`,r=i`
  0% {
    transform: rotate(0deg);
    opacity: 0.1;
  }
  100% {
    transform: rotate(1turn);
    opacity: 1;
  }
`,x=()=>o.jsx(e,{children:o.jsx(t,{sx:{position:"relative",display:"flex",height:"100vh",width:"100%"},children:o.jsxs(t,{sx:{position:"absolute",left:"calc(50% - 35px)",top:"50%",width:"55px",height:"55px",borderRadius:"50%",boxSizing:"border-box",border:"3px solid transparent"},children:[o.jsx(t,{sx:{position:"absolute",width:"100%",height:"100%",border:"3px solid transparent",borderLeft:"3px solid #2962ff",borderRadius:"50%",boxSizing:"border-box",animation:`${s} 1s ease infinite`}}),o.jsx(t,{sx:{position:"absolute",width:"100%",height:"100%",border:"3px solid transparent",borderLeft:"3px solid #2962ff",borderRadius:"50%",boxSizing:"border-box",animation:`${r} 1s ease infinite 0.1s`}}),o.jsx(t,{sx:{position:"absolute",width:"100%",height:"100%",border:"3px solid transparent",borderLeft:"3px solid #2962ff",borderRadius:"50%",boxSizing:"border-box",animation:`${r} 1s ease infinite 0.2s`}})]})})});export{x as default};
