import{r as h,F as p,z as j,j as e}from"./index-BG5vYnqD.js";import{G as s,b as f,a as g,I as t}from"./IconifyIcon-CTD3m3sX.js";import{e as a,D as y}from"./TextField-BABxjnxz.js";import{L as d,T as o}from"./Link-CCDjZ_cV.js";import{a as r}from"./Button-DIC4O69G.js";import{B as v}from"./Box-Be8rAmCf.js";import{S as l}from"./Stack-ttnIWVIf.js";import{C as w}from"./Container-S3gDmVyJ.js";import{C as b}from"./Card-DUartk6E.js";import"./Portal-IcPWo0MN.js";import"./styled-U5Gkx0Di.js";const z=()=>{const[i,c]=h.useState(!1),m=p(),{up:x}=j(),n=x("sm"),u=()=>{m("/")};return e.jsxs(e.Fragment,{children:[e.jsxs(s,{container:!0,spacing:3,sx:{mb:2.5},children:[e.jsx(s,{item:!0,xs:12,children:e.jsx(a,{fullWidth:!0,size:n?"medium":"small",name:"email",label:"Email address"})}),e.jsx(s,{item:!0,xs:12,children:e.jsx(a,{fullWidth:!0,size:n?"medium":"small",name:"password",label:"Password",type:i?"text":"password",InputProps:{endAdornment:e.jsx(f,{position:"end",children:e.jsx(g,{onClick:()=>c(!i),edge:"end",children:e.jsx(t,{icon:i?"majesticons:eye":"majesticons:eye-off"})})})}})})]}),e.jsx(s,{container:!0,justifyContent:"flex-end",sx:{my:3},children:e.jsx(s,{item:!0,children:e.jsx(d,{href:"/authentication/forget-password",variant:"subtitle2",underline:"hover",children:"Forgot password?"})})}),e.jsx(r,{fullWidth:!0,size:n?"large":"medium",type:"submit",variant:"contained",color:"primary",onClick:u,children:"Login"})]})},T=()=>e.jsx(v,{sx:{width:1,position:"relative",zIndex:100},children:e.jsx(l,{alignItems:"center",justifyContent:"center",sx:{height:1},children:e.jsx(w,{maxWidth:"sm",children:e.jsxs(b,{sx:{p:{xs:3,sm:5},width:1},children:[e.jsx(o,{variant:"h4",children:"Sign In"}),e.jsxs(o,{variant:"body2",sx:{mt:2,mb:{xs:3,sm:5},fontSize:{xs:"subtitle1.fontSize",sm:"body2.fontSize"}},children:["Don’t have an account?",e.jsx(d,{href:"/authentication/sign-up",variant:"subtitle2",sx:{ml:.75,"&:hover":{color:"primary.light"}},children:"Create One Now!"})]}),e.jsxs(l,{direction:"row",spacing:{xs:1,sm:2},children:[e.jsx(r,{fullWidth:!0,size:"large",color:"neutral",variant:"outlined",sx:{p:1},children:e.jsx(t,{icon:"eva:google-fill",color:"error.main"})}),e.jsx(r,{fullWidth:!0,size:"large",color:"neutral",variant:"outlined",sx:{p:1},children:e.jsx(t,{icon:"gg:facebook",color:"primary.main",width:22})}),e.jsx(r,{fullWidth:!0,size:"large",color:"neutral",variant:"outlined",sx:{p:1},children:e.jsx(t,{icon:"logos:twitter"})})]}),e.jsx(y,{sx:{my:3},children:e.jsx(o,{variant:"body2",sx:{color:"text.secondary"},children:"OR"})}),e.jsx(z,{})]})})})});export{T as default};
