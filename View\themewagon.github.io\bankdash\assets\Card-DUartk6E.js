import{g as d,a as u,s as p,r as C,u as m,b as f,_ as n,j as x,d as v,e as y}from"./index-BG5vYnqD.js";import{P as R}from"./TextField-BABxjnxz.js";function g(s){return d("MuiCard",s)}u("MuiCard",["root"]);const h=["className","raised"],j=s=>{const{classes:o}=s;return y({root:["root"]},g,o)},M=p(R,{name:"MuiCard",slot:"Root",overridesResolver:(s,o)=>o.root})(()=>({overflow:"hidden"})),_=C.forwardRef(function(o,t){const e=m({props:o,name:"MuiCard"}),{className:i,raised:r=!1}=e,l=f(e,h),a=n({},e,{raised:r}),c=j(a);return x.jsx(M,n({className:v(c.root,i),elevation:r?8:void 0,ref:t,ownerState:a},l))});export{_ as C};
