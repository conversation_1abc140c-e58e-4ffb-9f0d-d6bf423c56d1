import{j as t,O as e}from"./index-BG5vYnqD.js";import{L as i}from"./Logo-DCRU6BJw.js";import{S as o}from"./Stack-ttnIWVIf.js";import{T as s}from"./Toolbar-xUiouFGd.js";import{L as m}from"./Link-CCDjZ_cV.js";import"./Image-BH4UcT_S.js";import"./Box-Be8rAmCf.js";import"./styled-U5Gkx0Di.js";const h=()=>t.jsxs(o,{direction:"row",sx:{position:"relative",minHeight:"100vh",bgcolor:"background.default",placeItems:"center",flexGrow:1,width:1,justifyContent:"center",background:r=>r.palette.gradients.bgGradient,px:{xs:.5,md:3},py:4},children:[t.jsx(s,{sx:{gap:1,minHeight:20,position:"fixed",top:{xs:10,sm:12,md:16},left:{xs:10,sm:12,md:16}},children:t.jsx(m,{href:"/",sx:{display:"flex",gap:2},children:t.jsx(i,{})})}),t.jsx(o,{direction:"row",sx:{mt:{xs:5,sm:4,md:3},width:"100%",display:"flex",justifyContent:"center"},children:t.jsx(e,{})})]});export{h as default};
