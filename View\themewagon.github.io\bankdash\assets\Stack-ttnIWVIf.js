import{V as R,_ as k,I as v,H as p,W as T,Q as F,X as B,r as d,G as D,b as G,j as M,d as E,Y as N,e as _,g as $,Z as h,s as O,u as U}from"./index-BG5vYnqD.js";import{s as A}from"./styled-U5Gkx0Di.js";const I=["component","direction","spacing","divider","children","className","useFlexGap"],L=R(),W=A("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,s)=>s.root});function H(e){return N({props:e,name:"<PERSON>iStack",defaultTheme:L})}function Q(e,s){const n=d.Children.toArray(e).filter(Boolean);return n.reduce((c,a,t)=>(c.push(a),t<n.length-1&&c.push(d.cloneElement(s,{key:`separator-${t}`})),c),[])}const X=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],Y=({ownerState:e,theme:s})=>{let n=k({display:"flex",flexDirection:"column"},v({theme:s},p({values:e.direction,breakpoints:s.breakpoints.values}),c=>({flexDirection:c})));if(e.spacing){const c=T(s),a=Object.keys(s.breakpoints.values).reduce((o,r)=>((typeof e.spacing=="object"&&e.spacing[r]!=null||typeof e.direction=="object"&&e.direction[r]!=null)&&(o[r]=!0),o),{}),t=p({values:e.direction,base:a}),m=p({values:e.spacing,base:a});typeof t=="object"&&Object.keys(t).forEach((o,r,i)=>{if(!t[o]){const u=r>0?t[i[r-1]]:"column";t[o]=u}}),n=F(n,v({theme:s},m,(o,r)=>e.useFlexGap?{gap:h(c,o)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${X(r?t[r]:e.direction)}`]:h(c,o)}}))}return n=B(s.breakpoints,n),n};function Z(e={}){const{createStyledComponent:s=W,useThemeProps:n=H,componentName:c="MuiStack"}=e,a=()=>_({root:["root"]},o=>$(c,o),{}),t=s(Y);return d.forwardRef(function(o,r){const i=n(o),l=D(i),{component:u="div",direction:x="column",spacing:j=0,divider:y,children:g,className:P,useFlexGap:C=!1}=l,S=G(l,I),V={direction:x,spacing:j,useFlexGap:C},b=a();return M.jsx(t,k({as:u,ownerState:V,ref:r,className:E(b.root,P)},S,{children:y?Q(g,y):g}))})}const J=Z({createStyledComponent:O("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,s)=>s.root}),useThemeProps:e=>U({props:e,name:"MuiStack"})});export{J as S};
