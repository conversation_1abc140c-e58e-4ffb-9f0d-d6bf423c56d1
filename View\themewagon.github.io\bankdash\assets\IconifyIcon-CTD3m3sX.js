import{a as ne,g as oe,s as re,c as E,_ as I,i as xe,r as v,u as ie,b as se,j as $,d as ce,e as ae,G as Xe,H as V,I as le}from"./index-BG5vYnqD.js";import{B as Ye}from"./Button-DIC4O69G.js";import{u as et,q as tt,H as nt}from"./TextField-BABxjnxz.js";import{T as ot}from"./Link-CCDjZ_cV.js";import{B as rt}from"./Box-Be8rAmCf.js";function it(e){return oe("MuiIconButton",e)}const st=ne("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge"]),ct=["edge","children","className","color","disabled","disableFocusRipple","size"],at=e=>{const{classes:t,disabled:n,color:r,edge:o,size:i}=e,s={root:["root",n&&"disabled",r!=="default"&&`color${E(r)}`,o&&`edge${E(o)}`,`size${E(i)}`]};return ae(s,it,t)},lt=re(Ye,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.color!=="default"&&t[`color${E(n.color)}`],n.edge&&t[`edge${E(n.edge)}`],t[`size${E(n.size)}`]]}})(({theme:e,ownerState:t})=>I({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",overflow:"visible",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest})},!t.disableRipple&&{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:xe(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}}},t.edge==="start"&&{marginLeft:t.size==="small"?-3:-12},t.edge==="end"&&{marginRight:t.size==="small"?-3:-12}),({theme:e,ownerState:t})=>{var n;const r=(n=(e.vars||e).palette)==null?void 0:n[t.color];return I({},t.color==="inherit"&&{color:"inherit"},t.color!=="inherit"&&t.color!=="default"&&I({color:r==null?void 0:r.main},!t.disableRipple&&{"&:hover":I({},r&&{backgroundColor:e.vars?`rgba(${r.mainChannel} / ${e.vars.palette.action.hoverOpacity})`:xe(r.main,e.palette.action.hoverOpacity)},{"@media (hover: none)":{backgroundColor:"transparent"}})}),t.size==="small"&&{padding:5,fontSize:e.typography.pxToRem(18)},t.size==="large"&&{padding:12,fontSize:e.typography.pxToRem(28)},{[`&.${st.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled}})}),_n=v.forwardRef(function(t,n){const r=ie({props:t,name:"MuiIconButton"}),{edge:o=!1,children:i,className:s,color:c="default",disabled:a=!1,disableFocusRipple:l=!1,size:u="medium"}=r,f=se(r,ct),d=I({},r,{edge:o,color:c,disabled:a,disableFocusRipple:l,size:u}),p=at(d);return $.jsx(lt,I({className:ce(p.root,s),centerRipple:!0,focusRipple:!l,disabled:a,ref:n},f,{ownerState:d,children:i}))}),be=v.createContext();function ut(e){return oe("MuiGrid",e)}const ft=[0,1,2,3,4,5,6,7,8,9,10],dt=["column-reverse","column","row-reverse","row"],pt=["nowrap","wrap-reverse","wrap"],N=["auto",!0,1,2,3,4,5,6,7,8,9,10,11,12],A=ne("MuiGrid",["root","container","item","zeroMinWidth",...ft.map(e=>`spacing-xs-${e}`),...dt.map(e=>`direction-xs-${e}`),...pt.map(e=>`wrap-xs-${e}`),...N.map(e=>`grid-xs-${e}`),...N.map(e=>`grid-sm-${e}`),...N.map(e=>`grid-md-${e}`),...N.map(e=>`grid-lg-${e}`),...N.map(e=>`grid-xl-${e}`)]),ht=["className","columns","columnSpacing","component","container","direction","item","rowSpacing","spacing","wrap","zeroMinWidth"];function O(e){const t=parseFloat(e);return`${t}${String(e).replace(String(t),"")||"px"}`}function gt({theme:e,ownerState:t}){let n;return e.breakpoints.keys.reduce((r,o)=>{let i={};if(t[o]&&(n=t[o]),!n)return r;if(n===!0)i={flexBasis:0,flexGrow:1,maxWidth:"100%"};else if(n==="auto")i={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"};else{const s=V({values:t.columns,breakpoints:e.breakpoints.values}),c=typeof s=="object"?s[o]:s;if(c==null)return r;const a=`${Math.round(n/c*1e8)/1e6}%`;let l={};if(t.container&&t.item&&t.columnSpacing!==0){const u=e.spacing(t.columnSpacing);if(u!=="0px"){const f=`calc(${a} + ${O(u)})`;l={flexBasis:f,maxWidth:f}}}i=I({flexBasis:a,flexGrow:0,maxWidth:a},l)}return e.breakpoints.values[o]===0?Object.assign(r,i):r[e.breakpoints.up(o)]=i,r},{})}function mt({theme:e,ownerState:t}){const n=V({values:t.direction,breakpoints:e.breakpoints.values});return le({theme:e},n,r=>{const o={flexDirection:r};return r.indexOf("column")===0&&(o[`& > .${A.item}`]={maxWidth:"none"}),o})}function Re({breakpoints:e,values:t}){let n="";Object.keys(t).forEach(o=>{n===""&&t[o]!==0&&(n=o)});const r=Object.keys(e).sort((o,i)=>e[o]-e[i]);return r.slice(0,r.indexOf(n))}function xt({theme:e,ownerState:t}){const{container:n,rowSpacing:r}=t;let o={};if(n&&r!==0){const i=V({values:r,breakpoints:e.breakpoints.values});let s;typeof i=="object"&&(s=Re({breakpoints:e.breakpoints.values,values:i})),o=le({theme:e},i,(c,a)=>{var l;const u=e.spacing(c);return u!=="0px"?{marginTop:`-${O(u)}`,[`& > .${A.item}`]:{paddingTop:O(u)}}:(l=s)!=null&&l.includes(a)?{}:{marginTop:0,[`& > .${A.item}`]:{paddingTop:0}}})}return o}function bt({theme:e,ownerState:t}){const{container:n,columnSpacing:r}=t;let o={};if(n&&r!==0){const i=V({values:r,breakpoints:e.breakpoints.values});let s;typeof i=="object"&&(s=Re({breakpoints:e.breakpoints.values,values:i})),o=le({theme:e},i,(c,a)=>{var l;const u=e.spacing(c);return u!=="0px"?{width:`calc(100% + ${O(u)})`,marginLeft:`-${O(u)}`,[`& > .${A.item}`]:{paddingLeft:O(u)}}:(l=s)!=null&&l.includes(a)?{}:{width:"100%",marginLeft:0,[`& > .${A.item}`]:{paddingLeft:0}}})}return o}function yt(e,t,n={}){if(!e||e<=0)return[];if(typeof e=="string"&&!Number.isNaN(Number(e))||typeof e=="number")return[n[`spacing-xs-${String(e)}`]];const r=[];return t.forEach(o=>{const i=e[o];Number(i)>0&&r.push(n[`spacing-${o}-${String(i)}`])}),r}const vt=re("div",{name:"MuiGrid",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e,{container:r,direction:o,item:i,spacing:s,wrap:c,zeroMinWidth:a,breakpoints:l}=n;let u=[];r&&(u=yt(s,l,t));const f=[];return l.forEach(d=>{const p=n[d];p&&f.push(t[`grid-${d}-${String(p)}`])}),[t.root,r&&t.container,i&&t.item,a&&t.zeroMinWidth,...u,o!=="row"&&t[`direction-xs-${String(o)}`],c!=="wrap"&&t[`wrap-xs-${String(c)}`],...f]}})(({ownerState:e})=>I({boxSizing:"border-box"},e.container&&{display:"flex",flexWrap:"wrap",width:"100%"},e.item&&{margin:0},e.zeroMinWidth&&{minWidth:0},e.wrap!=="wrap"&&{flexWrap:e.wrap}),mt,xt,bt,gt);function wt(e,t){if(!e||e<=0)return[];if(typeof e=="string"&&!Number.isNaN(Number(e))||typeof e=="number")return[`spacing-xs-${String(e)}`];const n=[];return t.forEach(r=>{const o=e[r];if(Number(o)>0){const i=`spacing-${r}-${String(o)}`;n.push(i)}}),n}const St=e=>{const{classes:t,container:n,direction:r,item:o,spacing:i,wrap:s,zeroMinWidth:c,breakpoints:a}=e;let l=[];n&&(l=wt(i,a));const u=[];a.forEach(d=>{const p=e[d];p&&u.push(`grid-${d}-${String(p)}`)});const f={root:["root",n&&"container",o&&"item",c&&"zeroMinWidth",...l,r!=="row"&&`direction-xs-${String(r)}`,s!=="wrap"&&`wrap-xs-${String(s)}`,...u]};return ae(f,ut,t)},Gn=v.forwardRef(function(t,n){const r=ie({props:t,name:"MuiGrid"}),{breakpoints:o}=et(),i=Xe(r),{className:s,columns:c,columnSpacing:a,component:l="div",container:u=!1,direction:f="row",item:d=!1,rowSpacing:p,spacing:m=0,wrap:b="wrap",zeroMinWidth:h=!1}=i,g=se(i,ht),k=p||m,y=a||m,S=v.useContext(be),j=u?c||12:S,x={},w=I({},g);o.keys.forEach(C=>{g[C]!=null&&(x[C]=g[C],delete w[C])});const T=I({},i,{columns:j,container:u,direction:f,item:d,rowSpacing:k,columnSpacing:y,wrap:b,zeroMinWidth:h,spacing:m},x,{breakpoints:o.keys}),M=St(T);return $.jsx(be.Provider,{value:j,children:$.jsx(vt,I({ownerState:T,className:ce(M.root,s),as:l,ref:n},w))})});function It(e){return oe("MuiInputAdornment",e)}const ye=ne("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]);var ve;const Ct=["children","className","component","disablePointerEvents","disableTypography","position","variant"],kt=(e,t)=>{const{ownerState:n}=e;return[t.root,t[`position${E(n.position)}`],n.disablePointerEvents===!0&&t.disablePointerEvents,t[n.variant]]},$t=e=>{const{classes:t,disablePointerEvents:n,hiddenLabel:r,position:o,size:i,variant:s}=e,c={root:["root",n&&"disablePointerEvents",o&&`position${E(o)}`,s,r&&"hiddenLabel",i&&`size${E(i)}`]};return ae(c,It,t)},Et=re("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:kt})(({theme:e,ownerState:t})=>I({display:"flex",height:"0.01em",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(e.vars||e).palette.action.active},t.variant==="filled"&&{[`&.${ye.positionStart}&:not(.${ye.hiddenLabel})`]:{marginTop:16}},t.position==="start"&&{marginRight:8},t.position==="end"&&{marginLeft:8},t.disablePointerEvents===!0&&{pointerEvents:"none"})),Dn=v.forwardRef(function(t,n){const r=ie({props:t,name:"MuiInputAdornment"}),{children:o,className:i,component:s="div",disablePointerEvents:c=!1,disableTypography:a=!1,position:l,variant:u}=r,f=se(r,Ct),d=tt()||{};let p=u;u&&d.variant,d&&!p&&(p=d.variant);const m=I({},r,{hiddenLabel:d.hiddenLabel,size:d.size,disablePointerEvents:c,position:l,variant:p}),b=$t(m);return $.jsx(nt.Provider,{value:null,children:$.jsx(Et,I({as:s,ownerState:m,className:ce(b.root,i),ref:n},f,{children:typeof o=="string"&&!a?$.jsx(ot,{color:"text.secondary",children:o}):$.jsxs(v.Fragment,{children:[l==="start"?ve||(ve=$.jsx("span",{className:"notranslate",children:"​"})):null,o]})}))})}),ze=Object.freeze({left:0,top:0,width:16,height:16}),W=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),ue=Object.freeze({...ze,...W}),Q=Object.freeze({...ue,body:"",hidden:!1});function Tt(e,t){const n={};!e.hFlip!=!t.hFlip&&(n.hFlip=!0),!e.vFlip!=!t.vFlip&&(n.vFlip=!0);const r=((e.rotate||0)+(t.rotate||0))%4;return r&&(n.rotate=r),n}function we(e,t){const n=Tt(e,t);for(const r in Q)r in W?r in e&&!(r in n)&&(n[r]=W[r]):r in t?n[r]=t[r]:r in e&&(n[r]=e[r]);return n}function Pt(e,t){const n=e.icons,r=e.aliases||Object.create(null),o=Object.create(null);function i(s){if(n[s])return o[s]=[];if(!(s in o)){o[s]=null;const c=r[s]&&r[s].parent,a=c&&i(c);a&&(o[s]=[c].concat(a))}return o[s]}return Object.keys(n).concat(Object.keys(r)).forEach(i),o}function jt(e,t,n){const r=e.icons,o=e.aliases||Object.create(null);let i={};function s(c){i=we(r[c]||o[c],i)}return s(t),n.forEach(s),we(e,i)}function Le(e,t){const n=[];if(typeof e!="object"||typeof e.icons!="object")return n;e.not_found instanceof Array&&e.not_found.forEach(o=>{t(o,null),n.push(o)});const r=Pt(e);for(const o in r){const i=r[o];i&&(t(o,jt(e,o,i)),n.push(o))}return n}const z=/^[a-z0-9]+(-[a-z0-9]+)*$/,U=(e,t,n,r="")=>{const o=e.split(":");if(e.slice(0,1)==="@"){if(o.length<2||o.length>3)return null;r=o.shift().slice(1)}if(o.length>3||!o.length)return null;if(o.length>1){const c=o.pop(),a=o.pop(),l={provider:o.length>0?o[0]:r,prefix:a,name:c};return t&&!G(l)?null:l}const i=o[0],s=i.split("-");if(s.length>1){const c={provider:r,prefix:s.shift(),name:s.join("-")};return t&&!G(c)?null:c}if(n&&r===""){const c={provider:r,prefix:"",name:i};return t&&!G(c,n)?null:c}return null},G=(e,t)=>e?!!((e.provider===""||e.provider.match(z))&&(t&&e.prefix===""||e.prefix.match(z))&&e.name.match(z)):!1,Mt={provider:"",aliases:{},not_found:{},...ze};function K(e,t){for(const n in t)if(n in e&&typeof e[n]!=typeof t[n])return!1;return!0}function Ae(e){if(typeof e!="object"||e===null)return null;const t=e;if(typeof t.prefix!="string"||!e.icons||typeof e.icons!="object"||!K(e,Mt))return null;const n=t.icons;for(const o in n){const i=n[o];if(!o.match(z)||typeof i.body!="string"||!K(i,Q))return null}const r=t.aliases||Object.create(null);for(const o in r){const i=r[o],s=i.parent;if(!o.match(z)||typeof s!="string"||!n[s]&&!r[s]||!K(i,Q))return null}return t}const Se=Object.create(null);function Ot(e,t){return{provider:e,prefix:t,icons:Object.create(null),missing:new Set}}function P(e,t){const n=Se[e]||(Se[e]=Object.create(null));return n[t]||(n[t]=Ot(e,t))}function fe(e,t){return Ae(t)?Le(t,(n,r)=>{r?e.icons[n]=r:e.missing.add(n)}):[]}function Nt(e,t,n){try{if(typeof n.body=="string")return e.icons[t]={...n},!0}catch{}return!1}let B=!1;function Be(e){return typeof e=="boolean"&&(B=e),B}function Ie(e){const t=typeof e=="string"?U(e,!0,B):e;if(t){const n=P(t.provider,t.prefix),r=t.name;return n.icons[r]||(n.missing.has(r)?null:void 0)}}function Rt(e,t){const n=U(e,!0,B);if(!n)return!1;const r=P(n.provider,n.prefix);return Nt(r,n.name,t)}function zt(e,t){if(typeof e!="object")return!1;if(typeof t!="string"&&(t=e.provider||""),B&&!t&&!e.prefix){let o=!1;return Ae(e)&&(e.prefix="",Le(e,(i,s)=>{s&&Rt(i,s)&&(o=!0)})),o}const n=e.prefix;if(!G({provider:t,prefix:n,name:"a"}))return!1;const r=P(t,n);return!!fe(r,e)}const Fe=Object.freeze({width:null,height:null}),_e=Object.freeze({...Fe,...W}),Lt=/(-?[0-9.]*[0-9]+[0-9.]*)/g,At=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function Ce(e,t,n){if(t===1)return e;if(n=n||100,typeof e=="number")return Math.ceil(e*t*n)/n;if(typeof e!="string")return e;const r=e.split(Lt);if(r===null||!r.length)return e;const o=[];let i=r.shift(),s=At.test(i);for(;;){if(s){const c=parseFloat(i);isNaN(c)?o.push(i):o.push(Math.ceil(c*t*n)/n)}else o.push(i);if(i=r.shift(),i===void 0)return o.join("");s=!s}}function Bt(e,t="defs"){let n="";const r=e.indexOf("<"+t);for(;r>=0;){const o=e.indexOf(">",r),i=e.indexOf("</"+t);if(o===-1||i===-1)break;const s=e.indexOf(">",i);if(s===-1)break;n+=e.slice(o+1,i).trim(),e=e.slice(0,r).trim()+e.slice(s+1)}return{defs:n,content:e}}function Ft(e,t){return e?"<defs>"+e+"</defs>"+t:t}function _t(e,t,n){const r=Bt(e);return Ft(r.defs,t+r.content+n)}const Gt=e=>e==="unset"||e==="undefined"||e==="none";function Dt(e,t){const n={...ue,...e},r={..._e,...t},o={left:n.left,top:n.top,width:n.width,height:n.height};let i=n.body;[n,r].forEach(b=>{const h=[],g=b.hFlip,k=b.vFlip;let y=b.rotate;g?k?y+=2:(h.push("translate("+(o.width+o.left).toString()+" "+(0-o.top).toString()+")"),h.push("scale(-1 1)"),o.top=o.left=0):k&&(h.push("translate("+(0-o.left).toString()+" "+(o.height+o.top).toString()+")"),h.push("scale(1 -1)"),o.top=o.left=0);let S;switch(y<0&&(y-=Math.floor(y/4)*4),y=y%4,y){case 1:S=o.height/2+o.top,h.unshift("rotate(90 "+S.toString()+" "+S.toString()+")");break;case 2:h.unshift("rotate(180 "+(o.width/2+o.left).toString()+" "+(o.height/2+o.top).toString()+")");break;case 3:S=o.width/2+o.left,h.unshift("rotate(-90 "+S.toString()+" "+S.toString()+")");break}y%2===1&&(o.left!==o.top&&(S=o.left,o.left=o.top,o.top=S),o.width!==o.height&&(S=o.width,o.width=o.height,o.height=S)),h.length&&(i=_t(i,'<g transform="'+h.join(" ")+'">',"</g>"))});const s=r.width,c=r.height,a=o.width,l=o.height;let u,f;s===null?(f=c===null?"1em":c==="auto"?l:c,u=Ce(f,a/l)):(u=s==="auto"?a:s,f=c===null?Ce(u,l/a):c==="auto"?l:c);const d={},p=(b,h)=>{Gt(h)||(d[b]=h.toString())};p("width",u),p("height",f);const m=[o.left,o.top,a,l];return d.viewBox=m.join(" "),{attributes:d,viewBox:m,body:i}}const Wt=/\sid="(\S+)"/g,Vt="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let Ut=0;function Ht(e,t=Vt){const n=[];let r;for(;r=Wt.exec(e);)n.push(r[1]);if(!n.length)return e;const o="suffix"+(Math.random()*16777216|Date.now()).toString(16);return n.forEach(i=>{const s=typeof t=="function"?t(i):t+(Ut++).toString(),c=i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");e=e.replace(new RegExp('([#;"])('+c+')([")]|\\.[a-z])',"g"),"$1"+s+o+"$3")}),e=e.replace(new RegExp(o,"g"),""),e}const J=Object.create(null);function Kt(e,t){J[e]=t}function Z(e){return J[e]||J[""]}function de(e){let t;if(typeof e.resources=="string")t=[e.resources];else if(t=e.resources,!(t instanceof Array)||!t.length)return null;return{resources:t,path:e.path||"/",maxURL:e.maxURL||500,rotate:e.rotate||750,timeout:e.timeout||5e3,random:e.random===!0,index:e.index||0,dataAfterTimeout:e.dataAfterTimeout!==!1}}const pe=Object.create(null),R=["https://api.simplesvg.com","https://api.unisvg.com"],D=[];for(;R.length>0;)R.length===1||Math.random()>.5?D.push(R.shift()):D.push(R.pop());pe[""]=de({resources:["https://api.iconify.design"].concat(D)});function qt(e,t){const n=de(t);return n===null?!1:(pe[e]=n,!0)}function he(e){return pe[e]}const Qt=()=>{let e;try{if(e=fetch,typeof e=="function")return e}catch{}};let ke=Qt();function Jt(e,t){const n=he(e);if(!n)return 0;let r;if(!n.maxURL)r=0;else{let o=0;n.resources.forEach(s=>{o=Math.max(o,s.length)});const i=t+".json?icons=";r=n.maxURL-o-n.path.length-i.length}return r}function Zt(e){return e===404}const Xt=(e,t,n)=>{const r=[],o=Jt(e,t),i="icons";let s={type:i,provider:e,prefix:t,icons:[]},c=0;return n.forEach((a,l)=>{c+=a.length+1,c>=o&&l>0&&(r.push(s),s={type:i,provider:e,prefix:t,icons:[]},c=a.length),s.icons.push(a)}),r.push(s),r};function Yt(e){if(typeof e=="string"){const t=he(e);if(t)return t.path}return"/"}const en=(e,t,n)=>{if(!ke){n("abort",424);return}let r=Yt(t.provider);switch(t.type){case"icons":{const i=t.prefix,c=t.icons.join(","),a=new URLSearchParams({icons:c});r+=i+".json?"+a.toString();break}case"custom":{const i=t.uri;r+=i.slice(0,1)==="/"?i.slice(1):i;break}default:n("abort",400);return}let o=503;ke(e+r).then(i=>{const s=i.status;if(s!==200){setTimeout(()=>{n(Zt(s)?"abort":"next",s)});return}return o=501,i.json()}).then(i=>{if(typeof i!="object"||i===null){setTimeout(()=>{i===404?n("abort",i):n("next",o)});return}setTimeout(()=>{n("success",i)})}).catch(()=>{n("next",o)})},tn={prepare:Xt,send:en};function nn(e){const t={loaded:[],missing:[],pending:[]},n=Object.create(null);e.sort((o,i)=>o.provider!==i.provider?o.provider.localeCompare(i.provider):o.prefix!==i.prefix?o.prefix.localeCompare(i.prefix):o.name.localeCompare(i.name));let r={provider:"",prefix:"",name:""};return e.forEach(o=>{if(r.name===o.name&&r.prefix===o.prefix&&r.provider===o.provider)return;r=o;const i=o.provider,s=o.prefix,c=o.name,a=n[i]||(n[i]=Object.create(null)),l=a[s]||(a[s]=P(i,s));let u;c in l.icons?u=t.loaded:s===""||l.missing.has(c)?u=t.missing:u=t.pending;const f={provider:i,prefix:s,name:c};u.push(f)}),t}function Ge(e,t){e.forEach(n=>{const r=n.loaderCallbacks;r&&(n.loaderCallbacks=r.filter(o=>o.id!==t))})}function on(e){e.pendingCallbacksFlag||(e.pendingCallbacksFlag=!0,setTimeout(()=>{e.pendingCallbacksFlag=!1;const t=e.loaderCallbacks?e.loaderCallbacks.slice(0):[];if(!t.length)return;let n=!1;const r=e.provider,o=e.prefix;t.forEach(i=>{const s=i.icons,c=s.pending.length;s.pending=s.pending.filter(a=>{if(a.prefix!==o)return!0;const l=a.name;if(e.icons[l])s.loaded.push({provider:r,prefix:o,name:l});else if(e.missing.has(l))s.missing.push({provider:r,prefix:o,name:l});else return n=!0,!0;return!1}),s.pending.length!==c&&(n||Ge([e],i.id),i.callback(s.loaded.slice(0),s.missing.slice(0),s.pending.slice(0),i.abort))})}))}let rn=0;function sn(e,t,n){const r=rn++,o=Ge.bind(null,n,r);if(!t.pending.length)return o;const i={id:r,icons:t,callback:e,abort:o};return n.forEach(s=>{(s.loaderCallbacks||(s.loaderCallbacks=[])).push(i)}),o}function cn(e,t=!0,n=!1){const r=[];return e.forEach(o=>{const i=typeof o=="string"?U(o,t,n):o;i&&r.push(i)}),r}var an={resources:[],index:0,timeout:2e3,rotate:750,random:!1,dataAfterTimeout:!1};function ln(e,t,n,r){const o=e.resources.length,i=e.random?Math.floor(Math.random()*o):e.index;let s;if(e.random){let x=e.resources.slice(0);for(s=[];x.length>1;){const w=Math.floor(Math.random()*x.length);s.push(x[w]),x=x.slice(0,w).concat(x.slice(w+1))}s=s.concat(x)}else s=e.resources.slice(i).concat(e.resources.slice(0,i));const c=Date.now();let a="pending",l=0,u,f=null,d=[],p=[];typeof r=="function"&&p.push(r);function m(){f&&(clearTimeout(f),f=null)}function b(){a==="pending"&&(a="aborted"),m(),d.forEach(x=>{x.status==="pending"&&(x.status="aborted")}),d=[]}function h(x,w){w&&(p=[]),typeof x=="function"&&p.push(x)}function g(){return{startTime:c,payload:t,status:a,queriesSent:l,queriesPending:d.length,subscribe:h,abort:b}}function k(){a="failed",p.forEach(x=>{x(void 0,u)})}function y(){d.forEach(x=>{x.status==="pending"&&(x.status="aborted")}),d=[]}function S(x,w,T){const M=w!=="success";switch(d=d.filter(C=>C!==x),a){case"pending":break;case"failed":if(M||!e.dataAfterTimeout)return;break;default:return}if(w==="abort"){u=T,k();return}if(M){u=T,d.length||(s.length?j():k());return}if(m(),y(),!e.random){const C=e.resources.indexOf(x.resource);C!==-1&&C!==e.index&&(e.index=C)}a="completed",p.forEach(C=>{C(T)})}function j(){if(a!=="pending")return;m();const x=s.shift();if(x===void 0){if(d.length){f=setTimeout(()=>{m(),a==="pending"&&(y(),k())},e.timeout);return}k();return}const w={status:"pending",resource:x,callback:(T,M)=>{S(w,T,M)}};d.push(w),l++,f=setTimeout(j,e.rotate),n(x,t,w.callback)}return setTimeout(j),g}function De(e){const t={...an,...e};let n=[];function r(){n=n.filter(c=>c().status==="pending")}function o(c,a,l){const u=ln(t,c,a,(f,d)=>{r(),l&&l(f,d)});return n.push(u),u}function i(c){return n.find(a=>c(a))||null}return{query:o,find:i,setIndex:c=>{t.index=c},getIndex:()=>t.index,cleanup:r}}function $e(){}const q=Object.create(null);function un(e){if(!q[e]){const t=he(e);if(!t)return;const n=De(t),r={config:t,redundancy:n};q[e]=r}return q[e]}function fn(e,t,n){let r,o;if(typeof e=="string"){const i=Z(e);if(!i)return n(void 0,424),$e;o=i.send;const s=un(e);s&&(r=s.redundancy)}else{const i=de(e);if(i){r=De(i);const s=e.resources?e.resources[0]:"",c=Z(s);c&&(o=c.send)}}return!r||!o?(n(void 0,424),$e):r.query(t,o,n)().abort}const Ee="iconify2",F="iconify",We=F+"-count",Te=F+"-version",Ve=36e5,dn=168,pn=50;function X(e,t){try{return e.getItem(t)}catch{}}function ge(e,t,n){try{return e.setItem(t,n),!0}catch{}}function Pe(e,t){try{e.removeItem(t)}catch{}}function Y(e,t){return ge(e,We,t.toString())}function ee(e){return parseInt(X(e,We))||0}const H={local:!0,session:!0},Ue={local:new Set,session:new Set};let me=!1;function hn(e){me=e}let _=typeof window>"u"?{}:window;function He(e){const t=e+"Storage";try{if(_&&_[t]&&typeof _[t].length=="number")return _[t]}catch{}H[e]=!1}function Ke(e,t){const n=He(e);if(!n)return;const r=X(n,Te);if(r!==Ee){if(r){const c=ee(n);for(let a=0;a<c;a++)Pe(n,F+a.toString())}ge(n,Te,Ee),Y(n,0);return}const o=Math.floor(Date.now()/Ve)-dn,i=c=>{const a=F+c.toString(),l=X(n,a);if(typeof l=="string"){try{const u=JSON.parse(l);if(typeof u=="object"&&typeof u.cached=="number"&&u.cached>o&&typeof u.provider=="string"&&typeof u.data=="object"&&typeof u.data.prefix=="string"&&t(u,c))return!0}catch{}Pe(n,a)}};let s=ee(n);for(let c=s-1;c>=0;c--)i(c)||(c===s-1?(s--,Y(n,s)):Ue[e].add(c))}function qe(){if(!me){hn(!0);for(const e in H)Ke(e,t=>{const n=t.data,r=t.provider,o=n.prefix,i=P(r,o);if(!fe(i,n).length)return!1;const s=n.lastModified||-1;return i.lastModifiedCached=i.lastModifiedCached?Math.min(i.lastModifiedCached,s):s,!0})}}function gn(e,t){const n=e.lastModifiedCached;if(n&&n>=t)return n===t;if(e.lastModifiedCached=t,n)for(const r in H)Ke(r,o=>{const i=o.data;return o.provider!==e.provider||i.prefix!==e.prefix||i.lastModified===t});return!0}function mn(e,t){me||qe();function n(r){let o;if(!H[r]||!(o=He(r)))return;const i=Ue[r];let s;if(i.size)i.delete(s=Array.from(i).shift());else if(s=ee(o),s>=pn||!Y(o,s+1))return;const c={cached:Math.floor(Date.now()/Ve),provider:e.provider,data:t};return ge(o,F+s.toString(),JSON.stringify(c))}t.lastModified&&!gn(e,t.lastModified)||Object.keys(t.icons).length&&(t.not_found&&(t=Object.assign({},t),delete t.not_found),n("local")||n("session"))}function je(){}function xn(e){e.iconsLoaderFlag||(e.iconsLoaderFlag=!0,setTimeout(()=>{e.iconsLoaderFlag=!1,on(e)}))}function bn(e,t){e.iconsToLoad?e.iconsToLoad=e.iconsToLoad.concat(t).sort():e.iconsToLoad=t,e.iconsQueueFlag||(e.iconsQueueFlag=!0,setTimeout(()=>{e.iconsQueueFlag=!1;const{provider:n,prefix:r}=e,o=e.iconsToLoad;delete e.iconsToLoad;let i;if(!o||!(i=Z(n)))return;i.prepare(n,r,o).forEach(c=>{fn(n,c,a=>{if(typeof a!="object")c.icons.forEach(l=>{e.missing.add(l)});else try{const l=fe(e,a);if(!l.length)return;const u=e.pendingIcons;u&&l.forEach(f=>{u.delete(f)}),mn(e,a)}catch(l){console.error(l)}xn(e)})})}))}const yn=(e,t)=>{const n=cn(e,!0,Be()),r=nn(n);if(!r.pending.length){let a=!0;return t&&setTimeout(()=>{a&&t(r.loaded,r.missing,r.pending,je)}),()=>{a=!1}}const o=Object.create(null),i=[];let s,c;return r.pending.forEach(a=>{const{provider:l,prefix:u}=a;if(u===c&&l===s)return;s=l,c=u,i.push(P(l,u));const f=o[l]||(o[l]=Object.create(null));f[u]||(f[u]=[])}),r.pending.forEach(a=>{const{provider:l,prefix:u,name:f}=a,d=P(l,u),p=d.pendingIcons||(d.pendingIcons=new Set);p.has(f)||(p.add(f),o[l][u].push(f))}),i.forEach(a=>{const{provider:l,prefix:u}=a;o[l][u].length&&bn(a,o[l][u])}),t?sn(t,r,i):je};function vn(e,t){const n={...e};for(const r in t){const o=t[r],i=typeof o;r in Fe?(o===null||o&&(i==="string"||i==="number"))&&(n[r]=o):i===typeof n[r]&&(n[r]=r==="rotate"?o%4:o)}return n}const wn=/[\s,]+/;function Sn(e,t){t.split(wn).forEach(n=>{switch(n.trim()){case"horizontal":e.hFlip=!0;break;case"vertical":e.vFlip=!0;break}})}function In(e,t=0){const n=e.replace(/^-?[0-9.]*/,"");function r(o){for(;o<0;)o+=4;return o%4}if(n===""){const o=parseInt(e);return isNaN(o)?0:r(o)}else if(n!==e){let o=0;switch(n){case"%":o=25;break;case"deg":o=90}if(o){let i=parseFloat(e.slice(0,e.length-n.length));return isNaN(i)?0:(i=i/o,i%1===0?r(i):0)}}return t}function Cn(e,t){let n=e.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const r in t)n+=" "+r+'="'+t[r]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+n+">"+e+"</svg>"}function kn(e){return e.replace(/"/g,"'").replace(/%/g,"%25").replace(/#/g,"%23").replace(/</g,"%3C").replace(/>/g,"%3E").replace(/\s+/g," ")}function $n(e){return"data:image/svg+xml,"+kn(e)}function En(e){return'url("'+$n(e)+'")'}let L;function Tn(){try{L=window.trustedTypes.createPolicy("iconify",{createHTML:e=>e})}catch{L=null}}function Pn(e){return L===void 0&&Tn(),L?L.createHTML(e):e}const Qe={..._e,inline:!1},jn={xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink","aria-hidden":!0,role:"img"},Mn={display:"inline-block"},te={backgroundColor:"currentColor"},Je={backgroundColor:"transparent"},Me={Image:"var(--svg)",Repeat:"no-repeat",Size:"100% 100%"},Oe={WebkitMask:te,mask:te,background:Je};for(const e in Oe){const t=Oe[e];for(const n in Me)t[e+n]=Me[n]}const On={...Qe,inline:!0};function Ne(e){return e+(e.match(/^[-0-9.]+$/)?"px":"")}const Nn=(e,t,n)=>{const r=t.inline?On:Qe,o=vn(r,t),i=t.mode||"svg",s={},c=t.style||{},a={...i==="svg"?jn:{}};if(n){const h=U(n,!1,!0);if(h){const g=["iconify"],k=["provider","prefix"];for(const y of k)h[y]&&g.push("iconify--"+h[y]);a.className=g.join(" ")}}for(let h in t){const g=t[h];if(g!==void 0)switch(h){case"icon":case"style":case"children":case"onLoad":case"mode":case"ssr":break;case"_ref":a.ref=g;break;case"className":a[h]=(a[h]?a[h]+" ":"")+g;break;case"inline":case"hFlip":case"vFlip":o[h]=g===!0||g==="true"||g===1;break;case"flip":typeof g=="string"&&Sn(o,g);break;case"color":s.color=g;break;case"rotate":typeof g=="string"?o[h]=In(g):typeof g=="number"&&(o[h]=g);break;case"ariaHidden":case"aria-hidden":g!==!0&&g!=="true"&&delete a["aria-hidden"];break;default:r[h]===void 0&&(a[h]=g)}}const l=Dt(e,o),u=l.attributes;if(o.inline&&(s.verticalAlign="-0.125em"),i==="svg"){a.style={...s,...c},Object.assign(a,u);let h=0,g=t.id;return typeof g=="string"&&(g=g.replace(/-/g,"_")),a.dangerouslySetInnerHTML={__html:Pn(Ht(l.body,g?()=>g+"ID"+h++:"iconifyReact"))},v.createElement("svg",a)}const{body:f,width:d,height:p}=e,m=i==="mask"||(i==="bg"?!1:f.indexOf("currentColor")!==-1),b=Cn(f,{...u,width:d+"",height:p+""});return a.style={...s,"--svg":En(b),width:Ne(u.width),height:Ne(u.height),...Mn,...m?te:Je,...c},v.createElement("span",a)};Be(!0);Kt("",tn);if(typeof document<"u"&&typeof window<"u"){qe();const e=window;if(e.IconifyPreload!==void 0){const t=e.IconifyPreload,n="Invalid IconifyPreload syntax.";typeof t=="object"&&t!==null&&(t instanceof Array?t:[t]).forEach(r=>{try{(typeof r!="object"||r===null||r instanceof Array||typeof r.icons!="object"||typeof r.prefix!="string"||!zt(r))&&console.error(n)}catch{console.error(n)}})}if(e.IconifyProviders!==void 0){const t=e.IconifyProviders;if(typeof t=="object"&&t!==null)for(let n in t){const r="IconifyProviders["+n+"] is invalid.";try{const o=t[n];if(typeof o!="object"||!o||o.resources===void 0)continue;qt(n,o)||console.error(r)}catch{console.error(r)}}}}function Ze(e){const[t,n]=v.useState(!!e.ssr),[r,o]=v.useState({});function i(p){if(p){const m=e.icon;if(typeof m=="object")return{name:"",data:m};const b=Ie(m);if(b)return{name:m,data:b}}return{name:""}}const[s,c]=v.useState(i(!!e.ssr));function a(){const p=r.callback;p&&(p(),o({}))}function l(p){if(JSON.stringify(s)!==JSON.stringify(p))return a(),c(p),!0}function u(){var p;const m=e.icon;if(typeof m=="object"){l({name:"",data:m});return}const b=Ie(m);if(l({name:m,data:b}))if(b===void 0){const h=yn([m],u);o({callback:h})}else b&&((p=e.onLoad)===null||p===void 0||p.call(e,m))}v.useEffect(()=>(n(!0),a),[]),v.useEffect(()=>{t&&u()},[e.icon,t]);const{name:f,data:d}=s;return d?Nn({...ue,...d},e,f):e.children?e.children:v.createElement("span",{})}const Rn=v.forwardRef((e,t)=>Ze({...e,_ref:t}));v.forwardRef((e,t)=>Ze({inline:!0,...e,_ref:t}));const Wn=({icon:e,width:t=20,sx:n,...r})=>{const o={width:t,height:t,flexShrink:0,display:"inline-flex"};return $.jsx(rt,{component:Rn,icon:e,sx:{...o,...n},...r})};export{Gn as G,Wn as I,_n as a,Dn as b};
