import{r as d,F as g,z as w,j as s}from"./index-BG5vYnqD.js";import{G as r,b as m,a as c,I as e}from"./IconifyIcon-CTD3m3sX.js";import{e as a,D as y}from"./TextField-BABxjnxz.js";import{a as t}from"./Button-DIC4O69G.js";import{B as S}from"./Box-Be8rAmCf.js";import{S as x}from"./Stack-ttnIWVIf.js";import{C as v}from"./Container-S3gDmVyJ.js";import{C as z}from"./Card-DUartk6E.js";import{T as l,L as b}from"./Link-CCDjZ_cV.js";import"./Portal-IcPWo0MN.js";import"./styled-U5Gkx0Di.js";const C=()=>{const[n,u]=d.useState(!1),[o,h]=d.useState(!1),p=g(),{up:j}=w(),i=j("sm"),f=()=>{p("/")};return s.jsxs(s.Fragment,{children:[s.jsxs(r,{container:!0,spacing:3,sx:{mb:2.5},children:[s.jsx(r,{item:!0,xs:12,children:s.jsx(a,{fullWidth:!0,size:i?"medium":"small",name:"email",label:"Email address"})}),s.jsx(r,{item:!0,xs:12,children:s.jsx(a,{fullWidth:!0,size:i?"medium":"small",name:"password",label:"Password",type:n?"text":"password",sx:{size:{xs:"small",sm:"medium"}},InputProps:{endAdornment:s.jsx(m,{position:"end",children:s.jsx(c,{onClick:()=>u(!n),edge:"end",children:s.jsx(e,{icon:n?"majesticons:eye":"majesticons:eye-off"})})})}})}),s.jsx(r,{item:!0,xs:12,children:s.jsx(a,{fullWidth:!0,size:i?"medium":"small",name:"confirmPassword",label:"Confirm Password",type:o?"text":"password",InputProps:{endAdornment:s.jsx(m,{position:"end",children:s.jsx(c,{onClick:()=>h(!o),edge:"end",children:s.jsx(e,{icon:o?"majesticons:eye":"majesticons:eye-off"})})})}})})]}),s.jsx(t,{fullWidth:!0,size:i?"large":"medium",type:"submit",variant:"contained",color:"primary",onClick:f,children:"Sign Up"})]})},L=()=>s.jsx(S,{sx:{width:1,position:"relative",zIndex:100},children:s.jsx(x,{alignItems:"center",justifyContent:"center",sx:{height:1},children:s.jsx(v,{maxWidth:"sm",children:s.jsxs(z,{sx:{p:{xs:3,sm:5},width:1},children:[s.jsx(l,{variant:"h4",children:"Sign Up"}),s.jsxs(l,{sx:{mt:2,mb:{xs:3,sm:5},fontSize:{xs:"subtitle1.fontSize",sm:"body2.fontSize"}},children:["Already have an account?",s.jsx(b,{href:"/authentication/login",variant:"subtitle2",sx:{ml:.75,"&:hover":{color:"primary.light"}},children:"Sign In Now!"})]}),s.jsxs(x,{direction:"row",spacing:{xs:1,sm:2},children:[s.jsx(t,{fullWidth:!0,size:"large",color:"neutral",variant:"outlined",sx:{p:1},children:s.jsx(e,{icon:"eva:google-fill",color:"error.main"})}),s.jsx(t,{fullWidth:!0,size:"large",color:"neutral",variant:"outlined",sx:{p:1},children:s.jsx(e,{icon:"gg:facebook",color:"primary.main",width:22})}),s.jsx(t,{fullWidth:!0,size:"large",color:"neutral",variant:"outlined",sx:{p:1},children:s.jsx(e,{icon:"logos:twitter"})})]}),s.jsx(y,{sx:{my:3},children:s.jsx(l,{variant:"body2",sx:{color:"text.secondary"},children:"OR"})}),s.jsx(C,{})]})})})});export{L as default};
